import fs from "fs";
import path from "path";
import { NextRequest, NextResponse } from "next/server";

/**
 * GET /api/zip-boundaries
 * 获取ZIP code的地理边界数据
 *
 * 查询参数:
 * - zip: 特定的ZIP code (可选)
 * - state: 州代码，默认为CA (可选)
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const zipCode = searchParams.get("zip");

    console.log(
      "ZIP boundaries request for:",
      zipCode || "all California ZIP codes"
    );

    // 尝试读取真实的加州ZIP Code数据
    const filePath = path.join(
      process.cwd(),
      "data/california-zip-codes.geojson"
    );

    if (fs.existsSync(filePath)) {
      console.log("Loading real California ZIP code data...");
      const fileContents = fs.readFileSync(filePath, "utf8");
      const geoJsonData = JSON.parse(fileContents);

      // 如果请求特定的ZIP code，过滤数据
      if (zipCode && geoJsonData.features) {
        const filteredFeatures = geoJsonData.features.filter((feature: any) => {
          const featureZip =
            feature.properties?.ZCTA5CE10 ||
            feature.properties?.zipCode ||
            feature.properties?.zip_code;
          return featureZip === zipCode;
        });

        return NextResponse.json({
          success: true,
          data: {
            type: "FeatureCollection",
            features: filteredFeatures,
          },
          count: filteredFeatures.length,
          source: "real_data",
          timestamp: new Date().toISOString(),
        });
      }

      // 返回所有数据
      const featureCount = geoJsonData.features
        ? geoJsonData.features.length
        : 0;
      console.log(`Loaded ${featureCount} ZIP code boundaries`);

      return NextResponse.json({
        success: true,
        data: geoJsonData,
        count: featureCount,
        source: "real_data",
        timestamp: new Date().toISOString(),
      });
    } else {
      // 如果文件不存在，使用示例数据
      console.log("Real data file not found, using sample data...");

      if (zipCode) {
        const sampleBoundary = createSampleZipBoundary(zipCode);
        return NextResponse.json({
          success: true,
          data: {
            type: "FeatureCollection",
            features: [sampleBoundary],
          },
          count: 1,
          source: "sample_data",
          note: 'Run "node scripts/download-zip-data.js" to download real data',
          timestamp: new Date().toISOString(),
        });
      }

      const sampleBoundaries = createSampleCaliforniaZipBoundaries();
      return NextResponse.json({
        success: true,
        data: {
          type: "FeatureCollection",
          features: sampleBoundaries,
        },
        count: sampleBoundaries.length,
        source: "sample_data",
        note: 'Run "node scripts/download-zip-data.js" to download real data',
        timestamp: new Date().toISOString(),
      });
    }
  } catch (error) {
    console.error("Error fetching ZIP boundaries:", error);

    return NextResponse.json(
      {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to fetch ZIP boundaries",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// 创建示例ZIP边界数据
function createSampleZipBoundary(zipCode: string) {
  // 基于ZIP code创建一个大致的边界框
  // 这里使用一些加州常见ZIP codes的大致位置
  const zipLocations: { [key: string]: [number, number] } = {
    "90210": [-118.4065, 34.0901], // Beverly Hills
    "94102": [-122.4194, 37.7749], // San Francisco
    "90401": [-118.4912, 34.0195], // Santa Monica
    "92101": [-117.1611, 32.7157], // San Diego
    "95014": [-122.0322, 37.323], // Cupertino
    "91210": [-118.2437, 34.1522], // Glendale
    "94301": [-122.143, 37.4419], // Palo Alto
    "90024": [-118.4412, 34.0631], // Westwood
  };

  const center = zipLocations[zipCode] || [-118.2437, 34.0522]; // Default to LA
  const [lng, lat] = center;

  // 创建一个大约0.02度的边界框（约2km x 2km）
  const offset = 0.01;

  return {
    type: "Feature",
    properties: {
      zip_code: zipCode,
      name: `ZIP Code ${zipCode}`,
      source: "sample",
    },
    geometry: {
      type: "Polygon",
      coordinates: [
        [
          [lng - offset, lat - offset],
          [lng + offset, lat - offset],
          [lng + offset, lat + offset],
          [lng - offset, lat + offset],
          [lng - offset, lat - offset],
        ],
      ],
    },
  };
}

// 创建一些示例加州ZIP边界
function createSampleCaliforniaZipBoundaries() {
  const sampleZips = [
    "90210",
    "94102",
    "90401",
    "92101",
    "95014",
    "91210",
    "94301",
    "90024",
  ];
  return sampleZips.map((zip) => createSampleZipBoundary(zip));
}

/**
 * OPTIONS /api/zip-boundaries
 * CORS预检请求
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type",
    },
  });
}
